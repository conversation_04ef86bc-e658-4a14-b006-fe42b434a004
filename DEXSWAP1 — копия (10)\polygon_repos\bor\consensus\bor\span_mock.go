// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/ethereum/go-ethereum/consensus/bor (interfaces: Spanner)

// Package bor is a generated GoMock package.
package bor

import (
	context "context"
	reflect "reflect"

	types "github.com/0xPolygon/heimdall-v2/x/stake/types"
	common "github.com/ethereum/go-ethereum/common"
	span "github.com/ethereum/go-ethereum/consensus/bor/heimdall/span"
	valset "github.com/ethereum/go-ethereum/consensus/bor/valset"
	core "github.com/ethereum/go-ethereum/core"
	state "github.com/ethereum/go-ethereum/core/state"
	types0 "github.com/ethereum/go-ethereum/core/types"
	rpc "github.com/ethereum/go-ethereum/rpc"
	gomock "github.com/golang/mock/gomock"
)

// MockSpanner is a mock of Spanner interface.
type MockSpanner struct {
	ctrl     *gomock.Controller
	recorder *MockSpannerMockRecorder
}

// MockSpannerMockRecorder is the mock recorder for MockSpanner.
type MockSpannerMockRecorder struct {
	mock *MockSpanner
}

// NewMockSpanner creates a new mock instance.
func NewMockSpanner(ctrl *gomock.Controller) *MockSpanner {
	mock := &MockSpanner{ctrl: ctrl}
	mock.recorder = &MockSpannerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSpanner) EXPECT() *MockSpannerMockRecorder {
	return m.recorder
}

// CommitSpan mocks base method.
func (m *MockSpanner) CommitSpan(arg0 context.Context, arg1 span.Span, arg2, arg3 []types.MinimalVal, arg4 *state.StateDB, arg5 *types0.Header, arg6 core.ChainContext) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CommitSpan", arg0, arg1, arg2, arg3, arg4, arg5, arg6)
	ret0, _ := ret[0].(error)
	return ret0
}

// CommitSpan indicates an expected call of CommitSpan.
func (mr *MockSpannerMockRecorder) CommitSpan(arg0, arg1, arg2, arg3, arg4, arg5, arg6 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CommitSpan", reflect.TypeOf((*MockSpanner)(nil).CommitSpan), arg0, arg1, arg2, arg3, arg4, arg5, arg6)
}

// GetCurrentSpan mocks base method.
func (m *MockSpanner) GetCurrentSpan(arg0 context.Context, arg1 common.Hash) (*span.Span, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCurrentSpan", arg0, arg1)
	ret0, _ := ret[0].(*span.Span)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCurrentSpan indicates an expected call of GetCurrentSpan.
func (mr *MockSpannerMockRecorder) GetCurrentSpan(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCurrentSpan", reflect.TypeOf((*MockSpanner)(nil).GetCurrentSpan), arg0, arg1)
}

// GetCurrentValidatorsByBlockNrOrHash mocks base method.
func (m *MockSpanner) GetCurrentValidatorsByBlockNrOrHash(arg0 context.Context, arg1 rpc.BlockNumberOrHash, arg2 uint64) ([]*valset.Validator, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCurrentValidatorsByBlockNrOrHash", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*valset.Validator)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCurrentValidatorsByBlockNrOrHash indicates an expected call of GetCurrentValidatorsByBlockNrOrHash.
func (mr *MockSpannerMockRecorder) GetCurrentValidatorsByBlockNrOrHash(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCurrentValidatorsByBlockNrOrHash", reflect.TypeOf((*MockSpanner)(nil).GetCurrentValidatorsByBlockNrOrHash), arg0, arg1, arg2)
}

// GetCurrentValidatorsByHash mocks base method.
func (m *MockSpanner) GetCurrentValidatorsByHash(arg0 context.Context, arg1 common.Hash, arg2 uint64) ([]*valset.Validator, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCurrentValidatorsByHash", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*valset.Validator)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCurrentValidatorsByHash indicates an expected call of GetCurrentValidatorsByHash.
func (mr *MockSpannerMockRecorder) GetCurrentValidatorsByHash(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCurrentValidatorsByHash", reflect.TypeOf((*MockSpanner)(nil).GetCurrentValidatorsByHash), arg0, arg1, arg2)
}
