name: CI
on:
  push:
    branches:
      - 'master'
      - 'qa'
      - 'develop'
  pull_request:
    branches:
      - '**'
    types: [opened, synchronize]

concurrency:
  group: build-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  build:
    name: Build

    if: (github.event.action != 'closed' || github.event.pull_request.merged == true)
    strategy:
      matrix:
        os: [ubuntu-22.04] # List of OS: https://github.com/actions/virtual-environments
    runs-on: ${{ matrix.os }}

    steps:
      - uses: actions/checkout@v4
      - run: |
          git submodule update --init --recursive --force
          git fetch --no-tags --prune --depth=1 origin +refs/heads/master:refs/remotes/origin/master

      - uses: actions/setup-go@v5
        with:
          go-version: 1.24.3

      - name: Install dependencies on Linux
        if: runner.os == 'Linux'
        run: sudo apt update && sudo apt install build-essential

      - uses: actions/cache@v3
        with:
          path: |
            ~/.cache/go-build
            ~/Library/Caches/go-build
            ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: ${{ runner.os }}-go-

      - name: Build
        run: make all

  lint:
    if: (github.event.action != 'closed' || github.event.pull_request.merged == true)
    strategy:
      matrix:
        os: [ubuntu-22.04] # List of OS: https://github.com/actions/virtual-environments
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/checkout@v4
      - run: |
          git submodule update --init --recursive --force
          git fetch --no-tags --prune --depth=1 origin +refs/heads/master:refs/remotes/origin/master

      - uses: actions/setup-go@v5
        with:
          go-version: 1.24.3

      - name: Install dependencies on Linux
        if: runner.os == 'Linux'
        run: sudo apt update && sudo apt install build-essential

      - name: Golang-ci install
        if: runner.os == 'Linux'
        run: make lintci-deps

      - name: Lint
        if: runner.os == 'Linux'
        run: make lint

  unit-tests:
    if: (github.event.action != 'closed' || github.event.pull_request.merged == true)
    strategy:
      matrix:
        os: [ubuntu-22.04] # List of OS: https://github.com/actions/virtual-environments
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/checkout@v4
      - run: |
          git submodule update --init --recursive --force
          git fetch --no-tags --prune --depth=1 origin +refs/heads/master:refs/remotes/origin/master

      - uses: actions/setup-go@v5
        with:
          go-version: 1.24.3

      - name: Install dependencies on Linux
        if: runner.os == 'Linux'
        run: sudo apt update && sudo apt install build-essential

      - uses: actions/cache@v3
        with:
          path: |
            ~/.cache/go-build
            ~/Library/Caches/go-build
            ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: ${{ runner.os }}-go-

      - name: Test
        run: make test

      - uses: PaloAltoNetworks/upload-secure-artifact@main
        with:
          name: unitTest-coverage
          path: cover.out

  # - name: Data race tests
  #   run: make test-race

  # # TODO: make it work
  # - name: Reproducible build test
  #   run: |
  #       make geth
  #       shasum -a256 ./build/bin/geth > bor1.sha256
  #       make geth
  #       shasum -a256 ./build/bin/geth > bor2.sha256
  #       if ! cmp -s bor1.sha256 bor2.sha256; then
  #         echo >&2 "Reproducible build broken"; cat bor1.sha256; cat bor2.sha256; exit 1
  #       fi

  integration-tests:
    if: (github.event.action != 'closed' || github.event.pull_request.merged == true)
    strategy:
      matrix:
        os: [ubuntu-22.04] # List of OS: https://github.com/actions/virtual-environments
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/checkout@v4
      - run: |
          git submodule update --init --recursive --force
          git fetch --no-tags --prune --depth=1 origin +refs/heads/master:refs/remotes/origin/master

      - uses: actions/setup-go@v5
        with:
          go-version: 1.24.3

      - name: Install dependencies on Linux
        if: runner.os == 'Linux'
        run: sudo apt update && sudo apt install build-essential

      - uses: actions/cache@v4
        with:
          path: |
            ~/.cache/go-build
            ~/Library/Caches/go-build
            ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: ${{ runner.os }}-go-

      - name: test-integration
        run: make test-integration

      - uses: PaloAltoNetworks/upload-secure-artifact@main
        with:
          name: integrationTest-coverage
          path: cover.out

  codecov:
    if: (github.event.action != 'closed' || github.event.pull_request.merged == true)
    strategy:
      matrix:
        os: [ubuntu-22.04] # List of OS: https://github.com/actions/virtual-environments
    runs-on: ${{ matrix.os }}
    needs: [unit-tests, integration-tests]
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Download artifacts
        uses: actions/download-artifact@v4.1.8
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3

  e2e-tests:
    if: (github.event.action != 'closed' || github.event.pull_request.merged == true)
    strategy:
      matrix:
        os: [ubuntu-22.04] # List of OS: https://github.com/actions/virtual-environments
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/checkout@v4
        with:
          path: bor
      - name: Checkout submodules
        run: |
          cd bor
          git submodule update --init --recursive --force
          git fetch --no-tags --prune --depth=1 origin +refs/heads/master:refs/remotes/origin/master

      - uses: actions/setup-go@v5
        with:
          go-version: 1.24.3

      - name: Checkout matic-cli
        uses: actions/checkout@v4
        with:
          repository: maticnetwork/matic-cli
          ref: heimdall-v2
          path: matic-cli

      - name: Install dependencies on Linux
        if: runner.os == 'Linux'
        run: |
          sudo apt update
          sudo apt install build-essential
          curl https://raw.githubusercontent.com/creationix/nvm/master/install.sh | bash
          sudo apt install jq curl

      - uses: actions/setup-node@v3
        with:
          node-version: '18.19.0'
          cache: 'npm'
          cache-dependency-path: |
            matic-cli/package-lock.json
            matic-cli/devnet/code/contracts/package-lock.json
            matic-cli/devnet/code/genesis-contracts/package-lock.json
            matic-cli/devnet/code/genesis-contracts/matic-contracts/package-lock.json

      - name: Install solc-select
        run: |
          sudo apt update
          sudo apt install python3 python3-pip -y
          sudo ln -sf /usr/bin/python3 /usr/bin/python
          pip install solc-select

      - name: Install Solidity Version
        run: |
          solc-select install 0.5.17
          solc-select install 0.6.12
          solc-select use 0.5.17
          solc --version

      - name: Install Foundry
        uses: foundry-rs/foundry-toolchain@v1

      - name: Bootstrap devnet
        run: |
          cd matic-cli
          npm install --prefer-offline --no-audit --progress=false
          mkdir devnet
          cd devnet
          ../bin/matic-cli.js setup devnet -c ../../bor/.github/matic-cli-config.yml

      - name: Launch devnet
        run: |
          cd matic-cli/devnet
          bash ../util-scripts/docker/devnet_setup.sh
          cd -
          timeout 2m bash bor/integration-tests/bor_health.sh

      - name: Run smoke tests
        run: |
          echo "Deposit 100 matic for each account to bor network"
          cd matic-cli/devnet/devnet
          SCRIPT_ADDRESS=$(jq -r '.[0].address' signer-dump.json)
          SCRIPT_PRIVATE_KEY=$(jq -r '.[0].priv_key' signer-dump.json)
          cd ../code/pos-contracts
          CONTRACT_ADDRESS=$(jq -r .root.tokens.MaticToken contractAddresses.json)
          forge script scripts/matic-cli-scripts/Deposit.s.sol:MaticDeposit --rpc-url http://localhost:9545 --private-key $SCRIPT_PRIVATE_KEY --broadcast --sig "run(address,address,uint256)" $SCRIPT_ADDRESS $CONTRACT_ADDRESS 100000000000000000000
          cd ../../../..
          timeout 60m bash bor/integration-tests/smoke_test.sh

      - name: Run RPC Tests
        run: |
          echo "Starting RPC Tests..."
          timeout 5m bash bor/integration-tests/rpc_test.sh

      - name: Resolve absolute path for logs
        id: pathfix
        run: |
          echo "ABS_LOG_PATH=$(realpath matic-cli/devnet/logs)" >> $GITHUB_ENV

      - name: Upload logs
        if: always()
        uses: PaloAltoNetworks/upload-secure-artifact@main
        with:
          name: logs_${{ github.run_id }}
          path: ${{ env.ABS_LOG_PATH }}

      - name: Package code and chain data
        if: always()
        run: |
          cd matic-cli/devnet
          docker compose down --remove-orphans
          cd -
          mkdir -p ${{ github.run_id }}/matic-cli
          sudo mv bor ${{ github.run_id }}
          sudo mv matic-cli/devnet ${{ github.run_id }}/matic-cli
          sudo tar --warning=no-file-changed --exclude='.git' -czf code.tar.gz ${{ github.run_id }}

      - name: Upload code and chain data
        if: always()
        uses: PaloAltoNetworks/upload-secure-artifact@main
        with:
          name: code_${{ github.run_id }}
          path: code.tar.gz
