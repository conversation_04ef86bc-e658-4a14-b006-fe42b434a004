#!/usr/bin/env node

/**
 * 🔍 ПРОВЕРКА ПОЗИЦИЙ - БЕЗ ЗАКРЫТИЯ
 * Просто смотрим что у нас есть
 */

const { Connection, Keypair, PublicKey } = require('@solana/web3.js');
require('dotenv').config({ path: '.env.solana' });

// 🎯 КОНФИГУРАЦИЯ
const CONFIG = {
    RPC_URLS: [
        process.env.HELIUS_RPC_URL,
        process.env.QUICKNODE_RPC_URL_BACKUP2,
        process.env.QUICKNODE_RPC_URL,
        'https://api.mainnet-beta.solana.com'
    ].filter(Boolean),
    
    POSITIONS: [
        {
            address: 'CpzGtnguVbXN3PeWF2UAJbZrrhy5NDbiR9uu7ocPmw88',
            name: 'POOL_1',
            pool: '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6'
        },
        {
            address: '5oChsgM2EeSmt1mNuPJb91k4GWeZJ36bA65kZCgyRUEf',
            name: 'POOL_2',
            pool: 'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y'
        }
    ]
};

class PositionChecker {
    constructor() {
        console.log('🔍 ПРОВЕРКА ПОЗИЦИЙ METEORA');
        
        // RPC подключение
        this.connection = new Connection(CONFIG.RPC_URLS[0], 'confirmed');
        console.log(`🌐 RPC: ${CONFIG.RPC_URLS[0].substring(0, 50)}...`);
        
        // Загрузка кошелька
        this.loadWallet();
        
        console.log('✅ Готов к проверке\n');
    }

    loadWallet() {
        const privateKey = process.env.PRIVATE_KEY || process.env.WALLET_PRIVATE_KEY;
        if (!privateKey) {
            throw new Error('❌ PRIVATE_KEY не найден!');
        }

        try {
            const bs58 = require('bs58').default;
            this.wallet = Keypair.fromSecretKey(bs58.decode(privateKey));
        } catch (error) {
            try {
                this.wallet = Keypair.fromSecretKey(new Uint8Array(JSON.parse(privateKey)));
            } catch (jsonError) {
                throw new Error('❌ Неверный формат ключа');
            }
        }
        
        console.log(`✅ Wallet: ${this.wallet.publicKey.toString()}`);
    }

    async checkWalletBalance() {
        try {
            const balance = await this.connection.getBalance(this.wallet.publicKey);
            const solBalance = balance / 1e9;
            console.log(`💰 Баланс кошелька: ${solBalance.toFixed(6)} SOL`);
            return solBalance;
        } catch (error) {
            console.log(`❌ Ошибка проверки баланса: ${error.message}`);
            return 0;
        }
    }

    async checkPosition(position) {
        console.log(`\n📊 ${position.name}: ${position.address}`);
        console.log(`   Pool: ${position.pool}`);
        
        try {
            const pubkey = new PublicKey(position.address);
            const accountInfo = await this.connection.getAccountInfo(pubkey);
            
            if (!accountInfo) {
                console.log(`   ❌ Позиция не найдена`);
                return { exists: false };
            }
            
            const solAmount = accountInfo.lamports / 1e9;
            console.log(`   ✅ Позиция существует:`);
            console.log(`      💰 Lamports: ${accountInfo.lamports.toLocaleString()}`);
            console.log(`      💰 SOL: ${solAmount.toFixed(6)}`);
            console.log(`      📊 Размер данных: ${accountInfo.data.length} bytes`);
            console.log(`      🏛️ Owner: ${accountInfo.owner.toString()}`);
            console.log(`      🔒 Executable: ${accountInfo.executable}`);
            console.log(`      💸 Rent Epoch: ${accountInfo.rentEpoch}`);
            
            // Проверяем является ли это Meteora позицией
            const meteoraProgram = 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo';
            const isMeteora = accountInfo.owner.toString() === meteoraProgram;
            console.log(`      🌪️ Meteora позиция: ${isMeteora ? 'ДА' : 'НЕТ'}`);
            
            if (accountInfo.data.length > 0) {
                // Показываем первые байты данных
                const firstBytes = Array.from(accountInfo.data.slice(0, 16))
                    .map(b => b.toString(16).padStart(2, '0'))
                    .join(' ');
                console.log(`      📋 Первые байты: ${firstBytes}`);
            }
            
            return {
                exists: true,
                lamports: accountInfo.lamports,
                solAmount: solAmount,
                owner: accountInfo.owner.toString(),
                isMeteora: isMeteora,
                dataLength: accountInfo.data.length
            };
            
        } catch (error) {
            console.log(`   ❌ Ошибка проверки: ${error.message}`);
            return { exists: false, error: error.message };
        }
    }

    async run() {
        console.log('🔍 НАЧИНАЕМ ПРОВЕРКУ ПОЗИЦИЙ\n');
        
        // Проверяем баланс кошелька
        const walletBalance = await this.checkWalletBalance();
        
        let totalPositionValue = 0;
        let existingPositions = 0;
        
        // Проверяем каждую позицию
        for (const position of CONFIG.POSITIONS) {
            const result = await this.checkPosition(position);
            
            if (result.exists) {
                existingPositions++;
                totalPositionValue += result.solAmount || 0;
            }
            
            // Пауза между запросами
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
        // Итоги
        console.log(`\n🎉 ИТОГИ ПРОВЕРКИ:`);
        console.log(`   📊 Найдено позиций: ${existingPositions}/${CONFIG.POSITIONS.length}`);
        console.log(`   💰 Общая стоимость позиций: ${totalPositionValue.toFixed(6)} SOL`);
        console.log(`   💰 Баланс кошелька: ${walletBalance.toFixed(6)} SOL`);
        console.log(`   💵 Общие активы: ${(totalPositionValue + walletBalance).toFixed(6)} SOL`);
        
        if (existingPositions > 0) {
            console.log(`\n💡 РЕКОМЕНДАЦИИ:`);
            console.log(`   🔥 Позиции найдены! Можно попробовать их закрыть`);
            console.log(`   💰 Потенциальный возврат: ~${totalPositionValue.toFixed(6)} SOL`);
            console.log(`   💸 Комиссия за закрытие: ~${(existingPositions * 0.000005).toFixed(6)} SOL`);
            console.log(`   💵 Чистая прибыль: ~${(totalPositionValue - existingPositions * 0.000005).toFixed(6)} SOL`);
        } else {
            console.log(`\n⚠️ ПОЗИЦИИ НЕ НАЙДЕНЫ`);
            console.log(`   🔍 Возможные причины:`);
            console.log(`   - Позиции уже закрыты`);
            console.log(`   - Неправильные адреса`);
            console.log(`   - Позиции принадлежат другому кошельку`);
        }
        
        return existingPositions > 0;
    }
}

// 🚀 ЗАПУСК
async function main() {
    try {
        const checker = new PositionChecker();
        await checker.run();
        
        console.log('\n✅ ПРОВЕРКА ЗАВЕРШЕНА');
        
    } catch (error) {
        console.error(`\n💥 ОШИБКА: ${error.message}`);
        process.exit(1);
    }
}

main();
