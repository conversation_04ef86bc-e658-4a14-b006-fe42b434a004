#!/usr/bin/env node

/**
 * 🔥 ПРОСТЕЙШИЙ СКРИПТ ЗАКРЫТИЯ ПОЗИЦИЙ
 * ТОЛЬКО БАЗОВЫЕ SOLANA ФУНКЦИИ - БЕЗ SDK
 */

const { 
    Connection, 
    Keypair, 
    PublicKey, 
    Transaction,
    sendAndConfirmTransaction,
    SystemProgram
} = require('@solana/web3.js');

require('dotenv').config({ path: '.env.solana' });

// 🎯 КОНФИГУРАЦИЯ
const CONFIG = {
    // RPC URLs (в порядке приоритета)
    RPC_URLS: [
        process.env.HELIUS_RPC_URL,
        process.env.QUICKNODE_RPC_URL_BACKUP2,
        process.env.QUICKNODE_RPC_URL,
        'https://api.mainnet-beta.solana.com'
    ].filter(Boolean),
    
    // Позиции для закрытия (хардкод для надежности)
    POSITIONS: [
        {
            address: 'CpzGtnguVbXN3PeWF2UAJbZrrhy5NDbiR9uu7ocPmw88',
            name: 'POOL_1'
        },
        {
            address: '5oChsgM2EeSmt1mNuPJb91k4GWeZJ36bA65kZCgyRUEf',
            name: 'POOL_2'
        }
    ]
};

class SimplePositionCloser {
    constructor() {
        console.log('🔥 ПРОСТЕЙШИЙ ЗАКРЫВАТЕЛЬ ПОЗИЦИЙ');
        
        // 🌐 Подключение к RPC
        this.setupConnection();
        
        // 🔑 Загрузка кошелька
        this.loadWallet();
        
        console.log('✅ Инициализация завершена\n');
    }

    setupConnection() {
        for (const rpcUrl of CONFIG.RPC_URLS) {
            try {
                this.connection = new Connection(rpcUrl, 'confirmed');
                console.log(`🌐 RPC: ${rpcUrl.substring(0, 50)}...`);
                return;
            } catch (error) {
                console.log(`❌ RPC недоступен: ${rpcUrl}`);
            }
        }
        throw new Error('❌ Все RPC недоступны!');
    }

    loadWallet() {
        const privateKey = process.env.PRIVATE_KEY || process.env.WALLET_PRIVATE_KEY;
        if (!privateKey) {
            throw new Error('❌ PRIVATE_KEY не найден в .env!');
        }

        try {
            // BASE58 формат
            const bs58 = require('bs58').default;
            this.wallet = Keypair.fromSecretKey(bs58.decode(privateKey));
        } catch (error) {
            try {
                // JSON массив формат
                this.wallet = Keypair.fromSecretKey(new Uint8Array(JSON.parse(privateKey)));
            } catch (jsonError) {
                throw new Error(`❌ Неверный формат приватного ключа`);
            }
        }
        
        console.log(`✅ Wallet: ${this.wallet.publicKey.toString()}`);
    }

    /**
     * 🔍 ПРОВЕРКА БАЛАНСА КОШЕЛЬКА
     */
    async checkWalletBalance() {
        try {
            const balance = await this.connection.getBalance(this.wallet.publicKey);
            const solBalance = balance / 1e9;
            console.log(`💰 Баланс кошелька: ${solBalance.toFixed(6)} SOL`);
            
            if (solBalance < 0.001) {
                console.log('⚠️ ВНИМАНИЕ: Низкий баланс! Может не хватить на комиссии');
                return false;
            }
            return true;
        } catch (error) {
            console.log(`❌ Ошибка проверки баланса: ${error.message}`);
            return false;
        }
    }

    /**
     * 🔍 ПРОВЕРКА ПОЗИЦИИ
     */
    async checkPosition(positionAddress) {
        try {
            const pubkey = new PublicKey(positionAddress);
            const accountInfo = await this.connection.getAccountInfo(pubkey);
            
            if (!accountInfo) {
                return { exists: false, reason: 'Аккаунт не найден' };
            }
            
            return {
                exists: true,
                lamports: accountInfo.lamports,
                owner: accountInfo.owner.toString(),
                dataLength: accountInfo.data.length
            };
        } catch (error) {
            return { exists: false, reason: error.message };
        }
    }

    /**
     * 🔥 ЗАКРЫТИЕ ОДНОЙ ПОЗИЦИИ
     */
    async closePosition(positionAddress, positionName) {
        console.log(`\n📊 ${positionName}: ${positionAddress}`);
        
        // Проверяем позицию
        const check = await this.checkPosition(positionAddress);
        if (!check.exists) {
            console.log(`   ❌ Позиция не существует: ${check.reason}`);
            return false;
        }
        
        console.log(`   ✅ Позиция найдена: ${(check.lamports / 1e9).toFixed(6)} SOL`);
        
        try {
            // Получаем свежий blockhash
            const { blockhash } = await this.connection.getLatestBlockhash('finalized');
            
            // Создаем транзакцию закрытия аккаунта
            const transaction = new Transaction({
                recentBlockhash: blockhash,
                feePayer: this.wallet.publicKey
            });
            
            // Добавляем инструкцию закрытия
            transaction.add(
                SystemProgram.closeAccount({
                    fromPubkey: new PublicKey(positionAddress),
                    toPubkey: this.wallet.publicKey,
                    programId: new PublicKey(check.owner)
                })
            );
            
            console.log(`   ⚡ Отправка транзакции...`);
            
            // Отправляем транзакцию
            const signature = await sendAndConfirmTransaction(
                this.connection,
                transaction,
                [this.wallet],
                {
                    commitment: 'confirmed',
                    maxRetries: 3
                }
            );
            
            console.log(`   ✅ УСПЕШНО ЗАКРЫТА!`);
            console.log(`   📝 Signature: ${signature}`);
            console.log(`   💰 Возвращено: ${(check.lamports / 1e9).toFixed(6)} SOL`);
            
            return { success: true, lamports: check.lamports, signature };
            
        } catch (error) {
            console.log(`   ❌ Ошибка: ${error.message}`);
            return false;
        }
    }

    /**
     * 🚀 ГЛАВНАЯ ФУНКЦИЯ
     */
    async run() {
        console.log('🔥 НАЧИНАЕМ ЗАКРЫТИЕ ПОЗИЦИЙ\n');
        
        // Проверяем баланс
        const hasBalance = await this.checkWalletBalance();
        if (!hasBalance) {
            console.log('❌ Недостаточно SOL для операций');
            return false;
        }
        
        let successCount = 0;
        let totalRecovered = 0;
        
        // Закрываем каждую позицию
        for (const position of CONFIG.POSITIONS) {
            const result = await this.closePosition(position.address, position.name);
            
            if (result && result.success) {
                successCount++;
                totalRecovered += result.lamports;
            }
            
            // Пауза между операциями
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
        
        // Итоги
        console.log(`\n🎉 РЕЗУЛЬТАТЫ:`);
        console.log(`   ✅ Закрыто позиций: ${successCount}/${CONFIG.POSITIONS.length}`);
        console.log(`   💰 Общий возврат: ${(totalRecovered / 1e9).toFixed(6)} SOL`);
        console.log(`   💸 Комиссии: ~${(successCount * 0.000005).toFixed(6)} SOL`);
        console.log(`   💵 Чистая прибыль: ~${((totalRecovered / 1e9) - (successCount * 0.000005)).toFixed(6)} SOL`);
        
        return successCount > 0;
    }
}

// 🚀 ЗАПУСК
async function main() {
    try {
        const closer = new SimplePositionCloser();
        const success = await closer.run();
        
        console.log(success ? '\n✅ ГОТОВО!' : '\n❌ НИЧЕГО НЕ ЗАКРЫТО');
        process.exit(success ? 0 : 1);
        
    } catch (error) {
        console.error(`\n💥 ОШИБКА: ${error.message}`);
        process.exit(1);
    }
}

main();
