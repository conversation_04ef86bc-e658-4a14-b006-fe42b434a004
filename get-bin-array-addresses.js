/**
 * 🔥 ПОЛУЧЕНИЕ ТОЧНЫХ АДРЕСОВ BIN ARRAYS ДЛЯ ALT ТАБЛИЦЫ
 */

const { PublicKey } = require('@solana/web3.js');

class BinArrayAddressGenerator {
    constructor() {
        this.METEORA_PROGRAM_ID = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');
        this.BIN_ARRAY_SIZE = 70; // 70 бинов в каждом bin array
        
        // 🔥 НАШИ ПУЛЫ
        this.POOLS = {
            POOL_1: '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6',
            POOL_2: 'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y'
        };
        
        // 🔥 ПРИМЕРНЫЕ ДИАПАЗОНЫ НАШИХ БИНОВ (будут обновлены динамически)
        this.TYPICAL_BINS = {
            POOL_1: [-4280, -4279, -4278], // Примерный диапазон для Pool 1
            POOL_2: [-1710, -1709, -1708]  // Примерный диапазон для Pool 2
        };
    }

    /**
     * 🔥 ГЕНЕРАЦИЯ PDA ДЛЯ BIN ARRAY
     */
    async generateBinArrayPDA(poolAddress, binArrayIndex) {
        try {
            const indexBuffer = Buffer.alloc(8);
            indexBuffer.writeInt32LE(binArrayIndex, 0);

            const [binArrayPda] = await PublicKey.findProgramAddress(
                [
                    Buffer.from('bin_array'),
                    new PublicKey(poolAddress).toBuffer(),
                    indexBuffer
                ],
                this.METEORA_PROGRAM_ID
            );

            return binArrayPda;
        } catch (error) {
            console.error(`❌ Ошибка генерации PDA для index ${binArrayIndex}:`, error.message);
            return null;
        }
    }

    /**
     * 🔥 ПОЛУЧЕНИЕ ВСЕХ BIN ARRAY АДРЕСОВ ДЛЯ ПУЛА
     */
    async getBinArrayAddressesForPool(poolAddress, ourBins) {
        console.log(`🔥 ПОЛУЧАЕМ BIN ARRAY АДРЕСА ДЛЯ ПУЛА: ${poolAddress}`);
        console.log(`📊 Наши бины: ${ourBins.join(', ')}`);

        // 🔥 ВЫЧИСЛЯЕМ КАКИЕ BIN ARRAYS НУЖНЫ + СОСЕДНИЕ
        const requiredIndices = new Set();

        ourBins.forEach(binId => {
            const binArrayIndex = Math.floor(binId / this.BIN_ARRAY_SIZE);

            // Добавляем основной bin array
            requiredIndices.add(binArrayIndex);

            // Добавляем соседние bin arrays для ClaimFee2
            requiredIndices.add(binArrayIndex - 1); // Левый сосед
            requiredIndices.add(binArrayIndex + 1); // Правый сосед
        });

        console.log(`📊 Нужные bin array индексы (с соседними): ${Array.from(requiredIndices).sort((a,b) => a-b).join(', ')}`);

        // 🔥 ГЕНЕРИРУЕМ PDA ДЛЯ КАЖДОГО ИНДЕКСА
        const binArrayAddresses = [];
        
        for (const index of requiredIndices) {
            const pda = await this.generateBinArrayPDA(poolAddress, index);
            if (pda) {
                binArrayAddresses.push({
                    index: index,
                    address: pda.toString(),
                    publicKey: pda
                });
                console.log(`   ✅ Index ${index}: ${pda.toString()}`);
            }
        }

        return binArrayAddresses;
    }

    /**
     * 🔥 ПОЛУЧЕНИЕ ВСЕХ АДРЕСОВ ДЛЯ ОБОИХ ПУЛОВ
     */
    async getAllBinArrayAddresses() {
        console.log(`🔥 ПОЛУЧАЕМ ВСЕ BIN ARRAY АДРЕСА ДЛЯ ДОБАВЛЕНИЯ В ALT...`);
        
        const allAddresses = [];

        // Pool 1
        console.log(`\n📊 POOL 1: ${this.POOLS.POOL_1}`);
        const pool1Addresses = await this.getBinArrayAddressesForPool(
            this.POOLS.POOL_1, 
            this.TYPICAL_BINS.POOL_1
        );
        allAddresses.push(...pool1Addresses);

        // Pool 2
        console.log(`\n📊 POOL 2: ${this.POOLS.POOL_2}`);
        const pool2Addresses = await this.getBinArrayAddressesForPool(
            this.POOLS.POOL_2, 
            this.TYPICAL_BINS.POOL_2
        );
        allAddresses.push(...pool2Addresses);

        console.log(`\n✅ ВСЕГО ПОЛУЧЕНО ${allAddresses.length} BIN ARRAY АДРЕСОВ:`);
        allAddresses.forEach((addr, i) => {
            console.log(`${i + 1}. ${addr.address} (Pool: ${addr.index >= -62 ? 'POOL_2' : 'POOL_1'}, Index: ${addr.index})`);
        });

        return allAddresses;
    }

    /**
     * 🔥 ФОРМАТИРОВАНИЕ ДЛЯ ALT СКРИПТА
     */
    formatForALTScript(addresses) {
        console.log(`\n🔥 АДРЕСА ДЛЯ ДОБАВЛЕНИЯ В ALT ТАБЛИЦУ:`);
        console.log(`// 🔥 BIN ARRAY АДРЕСА ДЛЯ METEORA POOLS`);
        
        addresses.forEach((addr, i) => {
            const poolName = addr.index >= -62 ? 'POOL_2' : 'POOL_1';
            console.log(`'${addr.address}', // Bin Array ${addr.index} (${poolName})`);
        });
    }
}

// 🔥 ЗАПУСК СКРИПТА
async function main() {
    const generator = new BinArrayAddressGenerator();
    
    try {
        const addresses = await generator.getAllBinArrayAddresses();
        generator.formatForALTScript(addresses);
        
        console.log(`\n✅ ГОТОВО! Скопируй адреса выше в add-meteora-to-alt.js`);
        
    } catch (error) {
        console.error(`❌ Ошибка:`, error);
    }
}

if (require.main === module) {
    main();
}

module.exports = BinArrayAddressGenerator;
