# 🔥 METEORA DLMM ПАРСИНГ - ПОЛНОЕ РУКОВОДСТВО ПО ПОИСКУ НЕДОСТАЮЩИХ ПАРАМЕТРОВ

## 📊 АНАЛИЗ ТЕКУЩЕГО СОСТОЯНИЯ

### ✅ ЧТО У НАС ЕСТЬ:
1. **RPC запросы работают** - получаем данные пулов через `getMultipleAccountsInfo`
2. **Базовая структура кэша** - сохраняем и читаем данные
3. **Математические расчеты цен** - формула `(1 + binStep/10000)^binId` работает
4. **Отладочная информация** - видим hex данные и разные оффсеты

### ❌ ЧТО НЕ РАБОТАЕТ:

## 1. 🔥 ПАРСИНГ ДАННЫХ ПУЛА (LbPair Account)

### ПРОБЛЕМЫ:
- **Неправильные оффсеты** для `activeId` и `binStep`
- **Получаем огромные числа** вместо разумных значений (activeId=1976080 вместо ~-4000)
- **Нет проверки типа данных** (signed vs unsigned)

### НУЖНО НАЙТИ:
```
✅ activeId: правильный offset (сейчас получаем 1976080 вместо ~-4000)
✅ binStep: правильный offset (сейчас получаем 600 вместо 4-10)
✅ tokenXMint: адрес токена X (SOL)
✅ tokenYMint: адрес токена Y (USDC)
✅ reserveX: резерв токена X
✅ reserveY: резерв токена Y
✅ protocolFee: комиссия протокола
✅ baseFactor: базовый фактор
```

## 2. 🔥 ПАРСИНГ ДАННЫХ БИНОВ (Bin Account)

### ПРОБЛЕМЫ:
- **Нет реальных адресов бинов** - используем заглушки
- **Нет парсинга ликвидности** из реальных данных
- **Нет структуры Bin Account**

### НУЖНО НАЙТИ:
```
✅ amountX: количество токена X в бине
✅ amountY: количество токена Y в бине  
✅ liquiditySupply: общий supply ликвидности
✅ feeAmountXPerTokenStored: накопленные комиссии X
✅ feeAmountYPerTokenStored: накопленные комиссии Y
✅ rewardPerTokenStored: накопленные награды
```

## 3. 🔥 ДЕРИВАЦИЯ АДРЕСОВ

### ПРОБЛЕМЫ:
- **Нет функции деривации адресов бинов**
- **Нет seeds для создания PDA**
- **Используем заглушки вместо реальных адресов**

### НУЖНО НАЙТИ:
```
✅ Bin Account PDA seeds
✅ BinArray Account PDA seeds  
✅ Position Account PDA seeds
✅ Program ID для деривации
```

## 4. 🔥 КОНВЕРТАЦИЯ ДАННЫХ

### ПРОБЛЕМЫ:
- **Неправильная конвертация lamports в UI формат**
- **Нет учета decimals токенов**
- **Неправильные расчеты цен**

### НУЖНО НАЙТИ:
```
✅ SOL decimals: 9
✅ USDC decimals: 6
✅ Правильная формула конвертации цен
✅ Нормализация к базовой цене
```

---

## 🎯 ТЕХНИЧЕСКОЕ ЗАДАНИЕ ДЛЯ ПОИСКА

### ЗАДАЧА 1: НАЙТИ ПРАВИЛЬНУЮ СТРУКТУРУ LbPair ACCOUNT

**ЧТО ИСКАТЬ:**
1. **Официальную IDL** Meteora DLMM программы
2. **Anchor типы** для LbPair структуры
3. **Точные оффсеты** всех полей в байтах
4. **Примеры парсинга** из официального SDK

**ГДЕ ИСКАТЬ:**
- GitHub репозиторий Meteora DLMM SDK
- Anchor IDL файлы (.json)
- TypeScript типы в SDK
- Примеры кода в документации

### ЗАДАЧА 2: НАЙТИ СТРУКТУРУ BIN ACCOUNT

**ЧТО ИСКАТЬ:**
1. **Bin Account структуру** в IDL
2. **Оффсеты полей** amountX, amountY, supply
3. **Функции деривации** адресов бинов
4. **Seeds для PDA** создания

### ЗАДАЧА 3: НАЙТИ ФУНКЦИИ ДЕРИВАЦИИ АДРЕСОВ

**ЧТО ИСКАТЬ:**
1. **PDA seeds** для всех типов аккаунтов
2. **Program ID** Meteora DLMM
3. **Функции deriveBinArray**, **deriveBin**, **derivePosition**
4. **Примеры использования** в SDK

### ЗАДАЧА 4: НАЙТИ ПРАВИЛЬНЫЕ ФОРМУЛЫ РАСЧЕТОВ

**ЧТО ИСКАТЬ:**
1. **Формулу getPriceOfBinByBinId** с правильными параметрами
2. **Конвертацию lamports** в UI значения
3. **Нормализацию цен** к базовой валюте
4. **Расчет ликвидности** в USD эквиваленте

---

## 🔍 КОНКРЕТНЫЕ ФАЙЛЫ ДЛЯ ПОИСКА

### В METEORA SDK:
```
✅ /ts-client/src/dlmm/types/index.ts - TypeScript типы
✅ /ts-client/src/dlmm/idl/lb_clmm.json - IDL файл
✅ /ts-client/src/dlmm/helpers/derive.ts - функции деривации
✅ /ts-client/src/dlmm/helpers/math.ts - математические функции
✅ /ts-client/src/dlmm/helpers/weight.ts - расчет цен
✅ /ts-client/src/dlmm/constants.ts - константы программы
```

### В НАШЕМ КОДЕ:
```
✅ Найти рабочие примеры парсинга в бэкапах
✅ Проанализировать hex данные из отладки
✅ Сравнить с официальными примерами
✅ Найти паттерны в данных разных пулов
```

---

## 🎯 ПРИОРИТЕТНЫЙ ПЛАН ДЕЙСТВИЙ

### ЭТАП 1: КРИТИЧНЫЕ ДАННЫЕ (СЕЙЧАС)
1. **Найти правильные оффсеты** для activeId и binStep
2. **Исправить парсинг** данных пула
3. **Получить разумные значения** вместо огромных чисел

### ЭТАП 2: ЛИКВИДНОСТЬ (СЛЕДУЮЩИЙ)
1. **Найти структуру Bin Account**
2. **Реализовать деривацию адресов бинов**
3. **Парсить реальную ликвидность** из RPC данных

### ЭТАП 3: ТОЧНОСТЬ (ФИНАЛЬНЫЙ)
1. **Точные формулы расчета цен**
2. **Правильная конвертация в UI формат**
3. **Валидация всех данных**

---

## 🔥 НЕМЕДЛЕННЫЕ ДЕЙСТВИЯ

**НУЖНО ПРЯМО СЕЙЧАС:**
1. **Найти официальную IDL** Meteora DLMM
2. **Получить точные оффсеты** для LbPair полей
3. **Исправить парсинг** activeId и binStep
4. **Протестировать** на реальных данных

---

## 📝 ОТЛАДОЧНЫЕ ДАННЫЕ ИЗ ТЕКУЩЕГО ПАРСИНГА

### ПРОБЛЕМНЫЕ ЗНАЧЕНИЯ:
- activeId=1976080 (должно быть ~-4000)
- binStep=600 (должно быть 4-10)
- Цена=2.59e+50006 (должно быть ~$182)

### ПОТЕНЦИАЛЬНО ПРАВИЛЬНЫЕ ОФФСЕТЫ ИЗ ОТЛАДКИ:
- Offset 48: int32=-4264 (похоже на правильный activeId)
- Offset 32: uint16=500 (может быть binStep, но слишком большой)
- Offset 40: int32=8564 (возможный activeId)

### HEX ДАННЫЕ ПЕРВЫХ 64 БАЙТ:
```
21 0b 31 62 b5 65 b1 0d 10 27 1e 00 58 02 88 13 
40 9c 00 00 30 57 05 00 56 55 ff ff aa aa 00 00 
f4 01 00 00 00 00 00 00 85 8e 00 00 55 19 00 00 
59 f9 ff ff 00 00 00 00 9d 33 88 68 00 00 00 00
```

---

## 🎯 ЦЕЛЬ: ПОЛУЧИТЬ ПРАВИЛЬНЫЕ ДАННЫЕ

**ОЖИДАЕМЫЙ РЕЗУЛЬТАТ:**
- activeId: от -10000 до +10000
- binStep: от 1 до 100
- Цена SOL: от $100 до $300
- Ликвидность: реальные значения в WSOL и USDC

**КРИТЕРИЙ УСПЕХА:**
Система отображает разумные цены и ликвидность для обоих пулов без ошибок парсинга.
