# Description

Please provide a detailed description of what was done in this PR

# Changes

- [ ] Bugfix (non-breaking change that solves an issue)
- [ ] Hotfix (change that solves an urgent issue, and requires immediate attention)
- [ ] New feature (non-breaking change that adds functionality)
- [ ] Breaking change (change that is not backwards-compatible and/or changes current functionality)
- [ ] Changes only for a subset of nodes

# Breaking changes

Please complete this section if any breaking changes have been made, otherwise delete it

# Nodes audience

In case this PR includes changes that must be applied only to a subset of nodes, please specify how you handled it (e.g. by adding a flag with a default value...)

# Checklist

- [ ] I have added at least 2 reviewer or the whole pos-v1 team
- [ ] I have added sufficient documentation in code
- [ ] I will be resolving comments - if any - by pushing each fix in a separate commit and linking the commit hash in the comment reply
- [ ] Created a task in Jira and informed the team for implementation in Erigon client (if applicable)
- [ ] Includes RPC methods changes, and the Notion documentation has been updated

# Cross repository changes

- [ ] This PR requires changes to heimdall
    - In case link the PR here:
- [ ] This PR requires changes to matic-cli
    - In case link the PR here:

## Testing

- [ ] I have added unit tests
- [ ] I have added tests to CI
- [ ] I have tested this code manually on local environment
- [ ] I have tested this code manually on remote devnet using express-cli
- [ ] I have tested this code manually on amoy
- [ ] I have created new e2e tests into express-cli

### Manual tests

Please complete this section with the steps you performed if you ran manual tests for this functionality, otherwise delete it

# Additional comments

Please post additional comments in this section if you have them, otherwise delete it
