# Golang CircleCI 2.0 configuration file
# Check https://circleci.com/docs/2.0/language-go/ for more details

version: 2.1

executors:
  golang:
    docker:
      - image: circleci/golang:1.13
    working_directory: /go/src/github.com/ethereum/go-ethereum

jobs:
  build:
    executor: golang
    steps:
      - checkout # check out source code to working directory
      - run:
          command: make all
      - run:
          command: make test
