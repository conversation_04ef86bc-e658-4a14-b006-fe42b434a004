/**
 * 🔥 БЕЗОПАСНОЕ ОБНОВЛЕНИЕ ALT ТАБЛИЦЫ
 * ДОБАВЛЯЕТ ТОЛЬКО НЕДОСТАЮЩИЕ BIN ARRAY АДРЕСА
 * НЕ ПОРТИТ СУЩЕСТВУЮЩУЮ СТРУКТУРУ
 */

const {
    Connection,
    PublicKey,
    Keypair,
    TransactionMessage,
    VersionedTransaction,
    AddressLookupTableProgram
} = require('@solana/web3.js');
const fs = require('fs');
require('dotenv').config({ path: '.env.solana' });

async function updateALTSafely() {
    console.log('🔥 БЕЗОПАСНОЕ ОБНОВЛЕНИЕ ALT ТАБЛИЦЫ');
    console.log('🎯 ДОБАВЛЯЕМ ТОЛЬКО НЕДОСТАЮЩИЕ BIN ARRAY АДРЕСА');
    console.log('✅ НЕ ПОРТИМ СУЩЕСТВУЮЩУЮ СТРУКТУРУ');
    console.log('=' .repeat(80));

    try {
        // 1. Подключение к RPC
        console.log(`🔍 SOLANA_RPC_URL: ${process.env.SOLANA_RPC_URL ? 'ЗАГРУЖЕН' : 'НЕ НАЙДЕН'}`);
        
        if (!process.env.SOLANA_RPC_URL) {
            throw new Error('❌ SOLANA_RPC_URL не найден в .env.solana');
        }
        
        const connection = new Connection(process.env.SOLANA_RPC_URL);
        console.log('✅ Подключение к RPC установлено');

        // 2. Загружаем кошелек
        let wallet;
        try {
            const secretKey = JSON.parse(fs.readFileSync('wallet.json', 'utf8'));
            wallet = Keypair.fromSecretKey(new Uint8Array(secretKey));
            console.log(`✅ Кошелек загружен: ${wallet.publicKey.toString()}`);
        } catch (error) {
            throw new Error(`❌ Ошибка загрузки кошелька: ${error.message}`);
        }

        // 3. Кастомная ALT таблица
        const customALTAddress = new PublicKey('FAeyUf4AdG7vTQ2Q89r9hWBkPdYkykJKG4CxgqArBGXe');
        console.log(`📋 Кастомная ALT таблица: ${customALTAddress.toString()}`);

        // 4. BIN ARRAY АДРЕСА КОТОРЫЕ ХОТИМ ДОБАВИТЬ
        const newBinArrays = [
            '********************************************', // Bin Array -62 (POOL_1)
            'EpYmUETVQBMj9M9bqTRTLnwEqJxjon5Q7bVqoYdQZmeo', // Bin Array -63 (POOL_1)
            'HfqTUYiUAesMSpR8QDyyN9hjX1k669zwhEaedDYF14v4', // Bin Array -61 (POOL_1)
            '61SBJarzLYiXyeoDPNZofZzGTCCbxw7e29W4ufCjJSrD', // Bin Array -25 (POOL_2)
            'GoWQ6cHrK7Cq94FniGo1S5dcfbFK7F16uwUHwoapSJra', // Bin Array -26 (POOL_2)
            '4hpgpABwazsMwqa3Bm53tEGx9AneS2fNhY4uTgsS8eWa'  // Bin Array -24 (POOL_2)
        ];

        console.log(`🔑 ПРОВЕРЯЕМ 6 BIN ARRAY АДРЕСОВ:`);
        newBinArrays.forEach((addr, i) => {
            console.log(`   ${i + 1}. ${addr.slice(0,8)}...`);
        });

        // 5. ПРОВЕРЯЕМ ТЕКУЩЕЕ СОСТОЯНИЕ ALT ТАБЛИЦЫ
        console.log('\n🔍 ПРОВЕРКА ТЕКУЩЕГО СОСТОЯНИЯ ALT ТАБЛИЦЫ...');
        
        const lookupTableResponse = await connection.getAddressLookupTable(customALTAddress);
        
        if (!lookupTableResponse.value) {
            throw new Error('❌ ALT таблица не найдена!');
        }

        const currentAddresses = lookupTableResponse.value.state.addresses;
        console.log(`📊 Текущих адресов в ALT: ${currentAddresses.length}`);

        // 6. ОПРЕДЕЛЯЕМ КАКИЕ АДРЕСА УЖЕ ЕСТЬ
        const currentAddressStrings = currentAddresses.map(addr => addr.toString());
        const addressesToAdd = [];
        const alreadyExists = [];

        newBinArrays.forEach(addr => {
            if (currentAddressStrings.includes(addr)) {
                alreadyExists.push(addr);
            } else {
                addressesToAdd.push(addr);
            }
        });

        console.log(`\n📊 АНАЛИЗ АДРЕСОВ:`);
        console.log(`   ✅ Уже существует: ${alreadyExists.length}`);
        console.log(`   🔥 Нужно добавить: ${addressesToAdd.length}`);

        if (alreadyExists.length > 0) {
            console.log(`\n✅ УЖЕ В ТАБЛИЦЕ:`);
            alreadyExists.forEach((addr, i) => {
                console.log(`   ${i + 1}. ${addr.slice(0,8)}...`);
            });
        }

        if (addressesToAdd.length === 0) {
            console.log(`\n🎉 ВСЕ BIN ARRAY АДРЕСА УЖЕ В ALT ТАБЛИЦЕ!`);
            console.log(`✅ ОБНОВЛЕНИЕ НЕ ТРЕБУЕТСЯ!`);
            return;
        }

        console.log(`\n🔥 ДОБАВЛЯЕМ ${addressesToAdd.length} НОВЫХ АДРЕСОВ:`);
        addressesToAdd.forEach((addr, i) => {
            console.log(`   ${i + 1}. ${addr.slice(0,8)}...`);
        });

        // 7. СОЗДАЕМ ТРАНЗАКЦИЮ ДОБАВЛЕНИЯ
        const addressesToAddPubkeys = addressesToAdd.map(addr => new PublicKey(addr));
        
        const extendInstruction = AddressLookupTableProgram.extendLookupTable({
            payer: wallet.publicKey,
            authority: wallet.publicKey,
            lookupTable: customALTAddress,
            addresses: addressesToAddPubkeys,
        });

        console.log(`\n🔥 СОЗДАЕМ ТРАНЗАКЦИЮ ДОБАВЛЕНИЯ...`);
        
        const { blockhash } = await connection.getLatestBlockhash();
        
        const messageV0 = new TransactionMessage({
            payerKey: wallet.publicKey,
            recentBlockhash: blockhash,
            instructions: [extendInstruction],
        }).compileToV0Message();

        const transaction = new VersionedTransaction(messageV0);
        transaction.sign([wallet]);

        // 8. ОТПРАВЛЯЕМ ТРАНЗАКЦИЮ
        console.log(`🚀 ОТПРАВЛЯЕМ ТРАНЗАКЦИЮ...`);
        
        const signature = await connection.sendTransaction(transaction);
        console.log(`📝 Signature: ${signature}`);

        // 9. ЖДЕМ ПОДТВЕРЖДЕНИЯ
        console.log(`⏳ ЖДЕМ ПОДТВЕРЖДЕНИЯ...`);
        
        const confirmation = await connection.confirmTransaction(signature, 'confirmed');
        
        if (confirmation.value.err) {
            throw new Error(`❌ Транзакция провалилась: ${confirmation.value.err}`);
        }

        console.log(`\n🎉 УСПЕШНО ДОБАВЛЕНО ${addressesToAdd.length} BIN ARRAY АДРЕСОВ!`);
        console.log(`✅ ALT таблица обновлена без порчи структуры`);
        console.log(`📊 Общий размер таблицы: ${currentAddresses.length + addressesToAdd.length} адресов`);
        console.log(`💾 Экономия: ${addressesToAdd.length} × 31 bytes = ${addressesToAdd.length * 31} bytes`);

    } catch (error) {
        console.error(`❌ Ошибка обновления ALT:`, error.message);
        
        console.log(`\n💡 ВОЗМОЖНЫЕ РЕШЕНИЯ:`);
        console.log(`1. Проверьте права доступа к ALT таблице (authority)`);
        console.log(`2. Проверьте что кошелек является authority для ALT таблицы`);
        console.log(`3. Попробуйте позже (возможна временная проблема с RPC)`);
    }
}

// Запуск обновления
if (require.main === module) {
    updateALTSafely();
}

module.exports = { updateALTSafely };
