// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/ethereum/go-ethereum/consensus/bor/abi (interfaces: ABI)

// Package api is a generated GoMock package.
package abi

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockABI is a mock of ABI interface.
type MockABI struct {
	ctrl     *gomock.Controller
	recorder *MockABIMockRecorder
}

// MockABIMockRecorder is the mock recorder for MockABI.
type MockABIMockRecorder struct {
	mock *MockABI
}

// NewMockABI creates a new mock instance.
func NewMockABI(ctrl *gomock.Controller) *MockABI {
	mock := &MockABI{ctrl: ctrl}
	mock.recorder = &MockABIMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockABI) EXPECT() *MockABIMockRecorder {
	return m.recorder
}

// Pack mocks base method.
func (m *MockABI) Pack(arg0 string, arg1 ...interface{}) ([]byte, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Pack", varargs...)
	ret0, _ := ret[0].([]byte)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Pack indicates an expected call of Pack.
func (mr *MockABIMockRecorder) Pack(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Pack", reflect.TypeOf((*MockABI)(nil).Pack), varargs...)
}

// UnpackIntoInterface mocks base method.
func (m *MockABI) UnpackIntoInterface(arg0 interface{}, arg1 string, arg2 []byte) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnpackIntoInterface", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UnpackIntoInterface indicates an expected call of UnpackIntoInterface.
func (mr *MockABIMockRecorder) UnpackIntoInterface(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnpackIntoInterface", reflect.TypeOf((*MockABI)(nil).UnpackIntoInterface), arg0, arg1, arg2)
}
