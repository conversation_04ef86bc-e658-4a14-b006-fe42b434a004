#!/usr/bin/env node

/**
 * 🔍 АНАЛИЗ HEX ДАННЫХ METEORA DLMM ПУЛОВ
 * Цель: Найти правильные activeId и binStep для каждого пула
 */

// HEX данные из логов
const pool5rCf1DM8 = "21 0b 31 62 b5 65 b1 0d 10 27 1e 00 58 02 88 13 c0 d4 01 00 e0 93 04 00 78 55 fe ff 88 aa 01 00 f4 01 00 00 00 00 00 00 c1 68 00 00 a1 1a 00 00 6e ef ff ff 00 00 00 00 9f 3e 88 68 00 00 00 00 00 00 00 00 00 00 00 00 ff 04 00 00 70 ef ff ff 04 00 00 00 00 00 00 00";

const poolBGm1tav5 = "21 0b 31 62 b5 65 b1 0d 10 27 1e 00 58 02 88 13 40 9c 00 00 30 57 05 00 56 55 ff ff aa aa 00 00 f4 01 00 00 00 00 00 00 5f 01 00 00 5f 01 00 00 5f f9 ff ff 00 00 00 00 4c 3e 88 68 00 00 00 00 00 00 00 00 00 00 00 00 ff 0a 00 00 5f f9 ff ff 0a 00 00 01 10 27 00 00";

function hexToBuffer(hexString) {
    const bytes = hexString.split(' ').map(hex => parseInt(hex, 16));
    return Buffer.from(bytes);
}

function analyzePool(poolName, hexData) {
    console.log(`\n🔍 АНАЛИЗ ПУЛА ${poolName}:`);
    console.log('=' .repeat(50));
    
    const buffer = hexToBuffer(hexData);
    console.log(`📊 Размер данных: ${buffer.length} bytes`);
    
    // Анализируем различные оффсеты для activeId (int32) и binStep (uint16)
    const possibleOffsets = [
        { activeIdOffset: 8, binStepOffset: 12 },   // Из dlmm-pool-finder.js
        { activeIdOffset: 16, binStepOffset: 20 },
        { activeIdOffset: 24, binStepOffset: 28 },
        { activeIdOffset: 32, binStepOffset: 36 },
        { activeIdOffset: 40, binStepOffset: 44 },
        { activeIdOffset: 48, binStepOffset: 52 },
        { activeIdOffset: 56, binStepOffset: 60 },
        { activeIdOffset: 64, binStepOffset: 68 },
        { activeIdOffset: 72, binStepOffset: 76 },  // Текущий найденный
        { activeIdOffset: 72, binStepOffset: 80 },  // Текущий найденный
    ];
    
    console.log('\n📋 АНАЛИЗ ВСЕХ ВОЗМОЖНЫХ ОФФСЕТОВ:');
    
    for (const { activeIdOffset, binStepOffset } of possibleOffsets) {
        if (activeIdOffset + 4 <= buffer.length && binStepOffset + 2 <= buffer.length) {
            const activeId = buffer.readInt32LE(activeIdOffset);
            const binStep = buffer.readUInt16LE(binStepOffset);
            
            // Проверяем разумность значений
            const isValidActiveId = Math.abs(activeId) <= 20000 && activeId !== 0;
            const isValidBinStep = binStep > 0 && binStep <= 200;
            
            const status = (isValidActiveId && isValidBinStep) ? '✅' : '❌';
            
            console.log(`   ${status} Offset ${activeIdOffset}/${binStepOffset}: activeId=${activeId}, binStep=${binStep}`);
            
            if (isValidActiveId && isValidBinStep) {
                // Рассчитываем цену по формуле Meteora
                const price = Math.pow(1 + binStep/10000, activeId);
                console.log(`      💰 Рассчитанная цена: $${price.toFixed(6)}`);
                
                // Проверяем разумность цены для SOL
                if (price > 50 && price < 500) {
                    console.log(`      🎯 ЦЕНА В РАЗУМНОМ ДИАПАЗОНЕ ДЛЯ SOL!`);
                }
            }
        }
    }
    
    // Дополнительный анализ - ищем паттерны
    console.log('\n🔍 ПОИСК СПЕЦИФИЧНЫХ ПАТТЕРНОВ:');
    
    // Ищем отрицательные числа (возможные activeId)
    for (let i = 0; i <= buffer.length - 4; i += 4) {
        const value = buffer.readInt32LE(i);
        if (value < 0 && value > -20000) {
            console.log(`   🔍 Отрицательное число на offset ${i}: ${value} (возможный activeId)`);
        }
    }
    
    // Ищем маленькие положительные числа (возможные binStep)
    for (let i = 0; i <= buffer.length - 2; i += 2) {
        const value = buffer.readUInt16LE(i);
        if (value > 0 && value <= 200 && [1, 2, 4, 5, 10, 20, 25, 50, 100, 200].includes(value)) {
            console.log(`   🔍 Возможный binStep на offset ${i}: ${value}`);
        }
    }
}

// Анализируем оба пула
analyzePool('5rCf1DM8', pool5rCf1DM8);
analyzePool('BGm1tav5', poolBGm1tav5);

console.log('\n🎯 ВЫВОДЫ:');
console.log('=' .repeat(50));
console.log('1. Найти оффсеты где оба пула имеют РАЗНЫЕ валидные значения');
console.log('2. Проверить что рассчитанные цены разумны для SOL (~$180)');
console.log('3. Использовать эти оффсеты в коде парсинга');
