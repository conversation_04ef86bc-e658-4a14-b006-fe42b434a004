#!/usr/bin/env node

/**
 * 🔍 ПРОВЕРКА РЕАЛЬНОЙ СТОИМОСТИ ALT ОПЕРАЦИЙ
 * Анализируем почему цена такая высокая
 */

const { Connection, PublicKey } = require('@solana/web3.js');
require('dotenv').config({ path: '.env.solana' });

async function checkALTCosts() {
    console.log('🔍 АНАЛИЗ СТОИМОСТИ ALT ОПЕРАЦИЙ\n');
    
    try {
        const connection = new Connection(process.env.SOLANA_RPC_URL);
        
        // Твоя ALT таблица
        const altAddress = new PublicKey('FAeyUf4AdG7vTQ2Q89r9hWBkPdYkykJKG4CxgqArBGXe');
        
        console.log(`📋 ALT таблица: ${altAddress.toString()}`);
        
        // 1. Получаем информацию о таблице
        const altAccount = await connection.getAddressLookupTable(altAddress);
        
        if (!altAccount || !altAccount.value) {
            throw new Error('ALT таблица не найдена!');
        }
        
        const addresses = altAccount.value.state.addresses;
        console.log(`📊 Адресов в таблице: ${addresses.length}`);
        
        // 2. Рассчитываем размер данных
        const baseSize = 56; // Базовый размер ALT структуры
        const addressSize = 32; // Размер одного адреса
        const totalSize = baseSize + (addresses.length * addressSize);
        
        console.log(`📏 Размер данных:`);
        console.log(`   Базовый размер: ${baseSize} байт`);
        console.log(`   Адреса: ${addresses.length} × ${addressSize} = ${addresses.length * addressSize} байт`);
        console.log(`   Общий размер: ${totalSize} байт`);
        
        // 3. Рассчитываем rent
        const rentPerByte = await connection.getMinimumBalanceForRentExemption(1);
        const totalRent = await connection.getMinimumBalanceForRentExemption(totalSize);
        
        console.log(`\n💰 RENT расчеты:`);
        console.log(`   Rent за байт: ${rentPerByte} lamports`);
        console.log(`   Общий rent: ${totalRent} lamports (${totalRent / 1e9} SOL)`);
        
        // 4. Проверяем баланс ALT аккаунта
        const altBalance = await connection.getBalance(altAddress);
        console.log(`   Текущий баланс ALT: ${altBalance} lamports (${altBalance / 1e9} SOL)`);
        
        // 5. Рассчитываем стоимость добавления адресов
        console.log(`\n🔧 СТОИМОСТЬ ДОБАВЛЕНИЯ АДРЕСОВ:`);
        
        for (let newAddresses = 1; newAddresses <= 10; newAddresses++) {
            const newSize = totalSize + (newAddresses * addressSize);
            const newRent = await connection.getMinimumBalanceForRentExemption(newSize);
            const additionalRent = newRent - totalRent;
            
            console.log(`   +${newAddresses} адрес(ов): +${additionalRent} lamports (+${additionalRent / 1e9} SOL)`);
        }
        
        // 6. Проверяем последнюю транзакцию
        console.log(`\n🔍 АНАЛИЗ ПОСЛЕДНИХ ТРАНЗАКЦИЙ:`);
        
        const signatures = await connection.getSignaturesForAddress(altAddress, { limit: 5 });
        
        for (const sig of signatures) {
            console.log(`\n📝 Транзакция: ${sig.signature}`);
            console.log(`   Время: ${new Date(sig.blockTime * 1000).toLocaleString()}`);
            console.log(`   Статус: ${sig.err ? 'ОШИБКА' : 'УСПЕХ'}`);
            
            try {
                const tx = await connection.getTransaction(sig.signature, {
                    maxSupportedTransactionVersion: 0
                });
                
                if (tx) {
                    console.log(`   Комиссия: ${tx.meta.fee} lamports (${tx.meta.fee / 1e9} SOL)`);
                    
                    // Анализируем изменения баланса
                    if (tx.meta.preBalances && tx.meta.postBalances) {
                        const balanceChanges = tx.meta.postBalances.map((post, i) => 
                            post - tx.meta.preBalances[i]
                        );
                        
                        console.log(`   Изменения балансов:`);
                        balanceChanges.forEach((change, i) => {
                            if (change !== 0) {
                                const account = tx.transaction.message.staticAccountKeys?.[i] || 
                                              tx.transaction.message.accountKeys?.[i];
                                console.log(`     ${account}: ${change > 0 ? '+' : ''}${change} lamports`);
                            }
                        });
                    }
                }
            } catch (error) {
                console.log(`   ❌ Ошибка получения деталей: ${error.message}`);
            }
        }
        
        // 7. Выводы
        console.log(`\n🎯 ВЫВОДЫ:`);
        console.log(`✅ Базовая комиссия транзакции: ~5000 lamports (~0.000005 SOL)`);
        console.log(`✅ Rent за 1 адрес: ~${addressSize * rentPerByte} lamports (~${(addressSize * rentPerByte) / 1e9} SOL)`);
        console.log(`✅ Rent за 2 адреса: ~${2 * addressSize * rentPerByte} lamports (~${(2 * addressSize * rentPerByte) / 1e9} SOL)`);
        
        if (altBalance < totalRent) {
            console.log(`\n🚨 ПРОБЛЕМА: ALT аккаунт недофинансирован!`);
            console.log(`   Нужно: ${totalRent} lamports`);
            console.log(`   Есть: ${altBalance} lamports`);
            console.log(`   Не хватает: ${totalRent - altBalance} lamports (${(totalRent - altBalance) / 1e9} SOL)`);
        }
        
    } catch (error) {
        console.error('❌ Ошибка анализа:', error.message);
    }
}

if (require.main === module) {
    checkALTCosts();
}

module.exports = { checkALTCosts };
