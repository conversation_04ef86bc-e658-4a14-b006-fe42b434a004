#!/usr/bin/env node

/**
 * 🔥 САМОДОСТАТОЧНЫЙ СКРИПТ ЗАКРЫТИЯ ПОЗИЦИЙ
 * БЕЗ ПРОБЛЕМНОГО SDK - ПРЯМЫЕ RPC ВЫЗОВЫ
 */

const { 
    Connection, 
    Keypair, 
    PublicKey, 
    Transaction,
    TransactionInstruction,
    sendAndConfirmTransaction,
    SystemProgram
} = require('@solana/web3.js');

require('dotenv').config({ path: '.env.solana' });

class SelfSufficientPositionCloser {
    constructor() {
        console.log('🔥 САМОДОСТАТОЧНЫЙ ЗАКРЫВАТЕЛЬ ПОЗИЦИЙ - ИНИЦИАЛИЗАЦИЯ');
        
        // 🌐 RPC ПОДКЛЮЧЕНИЕ
        const rpcUrl = process.env.HELIUS_RPC_URL || 
                      process.env.QUICKNODE_RPC_URL_BACKUP2 || 
                      process.env.QUICKNODE_RPC_URL ||
                      'https://api.mainnet-beta.solana.com';
        
        this.connection = new Connection(rpcUrl, 'confirmed');
        console.log(`🌐 RPC: ${rpcUrl.substring(0, 50)}...`);

        // 🔑 ЗАГРУЗКА КОШЕЛЬКА
        this.loadWallet();

        // 🎯 ПОЗИЦИИ ДЛЯ ЗАКРЫТИЯ (ИЗ КОНФИГА)
        this.loadPositions();

        // 🔥 METEORA PROGRAM ID
        this.METEORA_PROGRAM = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');
        
        console.log('✅ Инициализация завершена');
    }

    loadWallet() {
        const privateKeyString = process.env.PRIVATE_KEY || process.env.WALLET_PRIVATE_KEY;
        if (!privateKeyString) {
            throw new Error('❌ PRIVATE_KEY не найден в .env файле!');
        }

        try {
            // Пробуем BASE58 формат
            const bs58 = require('bs58').default;
            this.wallet = Keypair.fromSecretKey(bs58.decode(privateKeyString));
            console.log(`✅ Wallet загружен (BASE58): ${this.wallet.publicKey.toString()}`);
        } catch (error) {
            try {
                // Fallback: JSON массив
                this.wallet = Keypair.fromSecretKey(new Uint8Array(JSON.parse(privateKeyString)));
                console.log(`✅ Wallet загружен (JSON): ${this.wallet.publicKey.toString()}`);
            } catch (jsonError) {
                throw new Error(`❌ Ошибка загрузки кошелька: ${error.message}`);
            }
        }
    }

    loadPositions() {
        try {
            const { getMeteoraPositions } = require('./trading-config');
            const centralizedPositions = getMeteoraPositions();
            
            this.positions = [
                {
                    address: new PublicKey(centralizedPositions.POOL_1),
                    pool: new PublicKey('5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6'),
                    name: 'POOL_1'
                },
                {
                    address: new PublicKey(centralizedPositions.POOL_2),
                    pool: new PublicKey('BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y'),
                    name: 'POOL_2'
                }
            ];
            
            console.log(`📊 Загружено ${this.positions.length} позиций из конфига`);
        } catch (error) {
            // Fallback: хардкод адреса
            console.log('⚠️ Не удалось загрузить из конфига, используем хардкод');
            this.positions = [
                {
                    address: new PublicKey('CpzGtnguVbXN3PeWF2UAJbZrrhy5NDbiR9uu7ocPmw88'),
                    pool: new PublicKey('5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6'),
                    name: 'POOL_1'
                },
                {
                    address: new PublicKey('5oChsgM2EeSmt1mNuPJb91k4GWeZJ36bA65kZCgyRUEf'),
                    pool: new PublicKey('BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y'),
                    name: 'POOL_2'
                }
            ];
        }
    }

    /**
     * 🔍 ПРОВЕРКА СУЩЕСТВОВАНИЯ ПОЗИЦИИ
     */
    async checkPositionExists(positionAddress) {
        try {
            const accountInfo = await this.connection.getAccountInfo(positionAddress);
            
            if (!accountInfo) {
                return { exists: false, reason: 'Аккаунт не найден' };
            }
            
            if (accountInfo.owner.toString() !== this.METEORA_PROGRAM.toString()) {
                return { exists: false, reason: 'Неправильный owner программы' };
            }
            
            if (accountInfo.data.length === 0) {
                return { exists: false, reason: 'Пустые данные аккаунта' };
            }
            
            return { 
                exists: true, 
                lamports: accountInfo.lamports,
                dataLength: accountInfo.data.length,
                owner: accountInfo.owner.toString()
            };
            
        } catch (error) {
            return { exists: false, reason: `RPC ошибка: ${error.message}` };
        }
    }

    /**
     * 🔥 СОЗДАНИЕ ИНСТРУКЦИИ ЗАКРЫТИЯ ПОЗИЦИИ
     */
    createClosePositionInstruction(positionAddress) {
        // 🔥 ПРОСТАЯ ИНСТРУКЦИЯ ЗАКРЫТИЯ АККАУНТА
        // Возвращает rent обратно владельцу
        return SystemProgram.closeAccount({
            fromPubkey: positionAddress,
            toPubkey: this.wallet.publicKey,
            programId: this.METEORA_PROGRAM
        });
    }

    /**
     * 🚀 ОСНОВНАЯ ФУНКЦИЯ ЗАКРЫТИЯ ПОЗИЦИЙ
     */
    async closeAllPositions() {
        console.log('\n🔥 НАЧИНАЕМ ЗАКРЫТИЕ ПОЗИЦИЙ');
        console.log(`💰 Wallet: ${this.wallet.publicKey.toString()}`);
        console.log(`📊 Позиций для проверки: ${this.positions.length}`);

        let successCount = 0;
        let totalRentRecovered = 0;

        for (const position of this.positions) {
            console.log(`\n📊 ${position.name}: ${position.address.toString()}`);
            console.log(`   Pool: ${position.pool.toString()}`);

            // 🔍 ПРОВЕРЯЕМ СУЩЕСТВОВАНИЕ ПОЗИЦИИ
            console.log(`   🔍 Проверка существования позиции...`);
            const positionCheck = await this.checkPositionExists(position.address);
            
            if (!positionCheck.exists) {
                console.log(`   ⚠️ Позиция не существует: ${positionCheck.reason}`);
                continue;
            }
            
            console.log(`   ✅ Позиция найдена:`);
            console.log(`      💰 Lamports: ${positionCheck.lamports}`);
            console.log(`      📊 Размер данных: ${positionCheck.dataLength} bytes`);
            console.log(`      🏛️ Owner: ${positionCheck.owner.slice(0, 8)}...`);

            try {
                // 🔥 ПОЛУЧАЕМ СВЕЖИЙ BLOCKHASH
                console.log(`   🔄 Получение свежего blockhash...`);
                const { blockhash, lastValidBlockHeight } = await this.connection.getLatestBlockhash('finalized');
                console.log(`   ✅ Blockhash: ${blockhash.slice(0, 20)}...`);

                // 🔥 СОЗДАЕМ ТРАНЗАКЦИЮ
                const transaction = new Transaction({
                    recentBlockhash: blockhash,
                    feePayer: this.wallet.publicKey
                });

                // 🔥 ДОБАВЛЯЕМ ИНСТРУКЦИЮ ЗАКРЫТИЯ
                const closeInstruction = this.createClosePositionInstruction(position.address);
                transaction.add(closeInstruction);

                console.log(`   📊 Транзакция создана: ${transaction.instructions.length} инструкций`);

                // 🔥 ОТПРАВЛЯЕМ ТРАНЗАКЦИЮ
                console.log(`   ⚡ Отправка транзакции...`);
                const signature = await sendAndConfirmTransaction(
                    this.connection,
                    transaction,
                    [this.wallet],
                    {
                        commitment: 'confirmed',
                        preflightCommitment: 'confirmed',
                        maxRetries: 3,
                        skipPreflight: false
                    }
                );

                console.log(`   ✅ ${position.name} ЗАКРЫТА УСПЕШНО!`);
                console.log(`   📝 Signature: ${signature}`);
                console.log(`   🔗 Explorer: https://solscan.io/tx/${signature}`);
                
                // Конвертируем lamports в SOL
                const solRecovered = positionCheck.lamports / 1e9;
                console.log(`   💰 Rent возвращен: ${solRecovered.toFixed(6)} SOL`);
                
                successCount++;
                totalRentRecovered += solRecovered;

                // Небольшая пауза между транзакциями
                await new Promise(resolve => setTimeout(resolve, 1000));

            } catch (error) {
                console.log(`   ❌ Ошибка закрытия ${position.name}:`);
                console.log(`      💥 ${error.message}`);
                
                if (error.message.includes('block height exceeded')) {
                    console.log(`      🔍 Blockhash истек, попробуйте еще раз`);
                } else if (error.message.includes('insufficient funds')) {
                    console.log(`      🔍 Недостаточно SOL для комиссии`);
                } else {
                    console.log(`      🔍 Неизвестная ошибка: ${error.constructor.name}`);
                }
            }
        }

        // 🎉 ИТОГИ
        console.log(`\n🎉 ЗАКРЫТИЕ ПОЗИЦИЙ ЗАВЕРШЕНО!`);
        console.log(`   ✅ Успешно закрыто: ${successCount}/${this.positions.length}`);
        console.log(`   💰 Общий возврат rent: ${totalRentRecovered.toFixed(6)} SOL`);
        console.log(`   💸 Примерная комиссия: ~${(successCount * 0.000005).toFixed(6)} SOL`);
        console.log(`   💵 Чистая прибыль: ~${(totalRentRecovered - successCount * 0.000005).toFixed(6)} SOL`);

        return successCount > 0;
    }
}

// 🚀 ЗАПУСК СКРИПТА
async function main() {
    console.log('🔥 САМОДОСТАТОЧНЫЙ ЗАКРЫВАТЕЛЬ ПОЗИЦИЙ - СТАРТ\n');
    
    try {
        const closer = new SelfSufficientPositionCloser();
        const success = await closer.closeAllPositions();
        
        if (success) {
            console.log('\n✅ СКРИПТ ЗАВЕРШЕН УСПЕШНО');
            process.exit(0);
        } else {
            console.log('\n⚠️ НИ ОДНА ПОЗИЦИЯ НЕ БЫЛА ЗАКРЫТА');
            process.exit(1);
        }
    } catch (error) {
        console.error('\n❌ КРИТИЧЕСКАЯ ОШИБКА:', error.message);
        process.exit(1);
    }
}

// Запуск с обработкой ошибок
main().catch(error => {
    console.error('💥 НЕОБРАБОТАННАЯ ОШИБКА:', error);
    process.exit(1);
});
