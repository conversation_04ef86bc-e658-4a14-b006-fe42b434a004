#!/usr/bin/env node

/**
 * 🔥 РАБОЧИЙ ЗАКРЫВАТЕЛЬ ПОЗИЦИЙ
 * Использует правильные методы Meteora SDK
 */

const { Connection, Keypair, PublicKey, sendAndConfirmTransaction } = require('@solana/web3.js');
const DLMM = require('@meteora-ag/dlmm').default;
require('dotenv').config({ path: '.env.solana' });

class WorkingPositionCloser {
    constructor() {
        console.log('🔥 РАБОЧИЙ ЗАКРЫВАТЕЛЬ ПОЗИЦИЙ METEORA');
        
        // RPC подключение
        const rpcUrl = process.env.HELIUS_RPC_URL || 
                      process.env.QUICKNODE_RPC_URL_BACKUP2 || 
                      process.env.QUICKNODE_RPC_URL ||
                      'https://api.mainnet-beta.solana.com';
        
        this.connection = new Connection(rpcUrl, 'confirmed');
        console.log(`🌐 RPC: ${rpcUrl.substring(0, 50)}...`);
        
        // Загрузка кошелька
        this.loadWallet();
        
        // Позиции
        this.positions = [
            {
                address: 'CpzGtnguVbXN3PeWF2UAJbZrrhy5NDbiR9uu7ocPmw88',
                pool: '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6',
                name: 'POOL_1'
            },
            {
                address: '5oChsgM2EeSmt1mNuPJb91k4GWeZJ36bA65kZCgyRUEf',
                pool: 'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y',
                name: 'POOL_2'
            }
        ];
        
        console.log('✅ Инициализация завершена\n');
    }

    loadWallet() {
        const privateKey = process.env.PRIVATE_KEY || process.env.WALLET_PRIVATE_KEY;
        if (!privateKey) {
            throw new Error('❌ PRIVATE_KEY не найден!');
        }

        try {
            const bs58 = require('bs58').default;
            this.wallet = Keypair.fromSecretKey(bs58.decode(privateKey));
        } catch (error) {
            try {
                this.wallet = Keypair.fromSecretKey(new Uint8Array(JSON.parse(privateKey)));
            } catch (jsonError) {
                throw new Error('❌ Неверный формат ключа');
            }
        }
        
        console.log(`✅ Wallet: ${this.wallet.publicKey.toString()}`);
    }

    async createDlmmPool(poolAddress, maxRetries = 3) {
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                console.log(`   🔄 Создание DLMM pool (попытка ${attempt}/${maxRetries})...`);
                const dlmmPool = await DLMM.create(this.connection, new PublicKey(poolAddress));
                console.log(`   ✅ DLMM pool создан успешно`);
                return dlmmPool;
            } catch (error) {
                console.log(`   ❌ Попытка ${attempt} неудачна: ${error.message}`);
                
                if (attempt === maxRetries) {
                    throw new Error(`Не удалось создать DLMM pool после ${maxRetries} попыток`);
                }
                
                // Пауза перед следующей попыткой
                await new Promise(resolve => setTimeout(resolve, attempt * 2000));
            }
        }
    }

    async closePosition(position) {
        console.log(`\n📊 ${position.name}: ${position.address}`);
        console.log(`   Pool: ${position.pool}`);
        
        try {
            // Создаем DLMM pool
            const dlmmPool = await this.createDlmmPool(position.pool);
            
            // Получаем позиции пользователя
            console.log(`   🔍 Поиск позиций пользователя...`);
            const { userPositions } = await dlmmPool.getPositionsByUserAndLbPair(this.wallet.publicKey);
            
            console.log(`   📊 Найдено позиций: ${userPositions.length}`);
            
            // Ищем нашу конкретную позицию
            const targetPosition = userPositions.find(pos => 
                pos.publicKey.toString() === position.address
            );
            
            if (!targetPosition) {
                console.log(`   ❌ Позиция ${position.address} не найдена среди позиций пользователя`);
                console.log(`   🔍 Доступные позиции:`);
                userPositions.forEach((pos, index) => {
                    console.log(`      ${index + 1}. ${pos.publicKey.toString()}`);
                });
                return false;
            }
            
            console.log(`   ✅ Позиция найдена!`);
            console.log(`      💰 X Amount: ${targetPosition.positionData.totalXAmount.toString()}`);
            console.log(`      💰 Y Amount: ${targetPosition.positionData.totalYAmount.toString()}`);
            
            // Проверяем есть ли ликвидность для удаления
            const hasLiquidity = !targetPosition.positionData.totalXAmount.isZero() || 
                               !targetPosition.positionData.totalYAmount.isZero();
            
            if (hasLiquidity) {
                console.log(`   🔄 Сначала удаляем ликвидность...`);
                
                // Удаляем всю ликвидность
                const removeLiquidityTx = await dlmmPool.removeLiquidity({
                    position: targetPosition.publicKey,
                    user: this.wallet.publicKey,
                    binIds: [], // Пустой массив означает удалить из всех бинов
                    liquiditiesBpsToRemove: [], // Пустой массив означает удалить всю ликвидность
                    shouldClaimAndClose: true // Закрыть позицию после удаления ликвидности
                });
                
                // Получаем свежий blockhash
                const { blockhash } = await this.connection.getLatestBlockhash('finalized');
                removeLiquidityTx.recentBlockhash = blockhash;
                
                console.log(`   ⚡ Отправка транзакции удаления ликвидности...`);
                const signature = await sendAndConfirmTransaction(
                    this.connection,
                    removeLiquidityTx,
                    [this.wallet],
                    { commitment: 'confirmed', maxRetries: 3 }
                );
                
                console.log(`   ✅ Ликвидность удалена и позиция закрыта!`);
                console.log(`   📝 Signature: ${signature}`);
                
                return { success: true, signature, method: 'removeLiquidity' };
                
            } else {
                console.log(`   🔄 Позиция пустая, просто закрываем...`);
                
                // Позиция пустая, просто закрываем
                const closePositionTx = await dlmmPool.closePosition({
                    owner: this.wallet.publicKey,
                    position: targetPosition.publicKey
                });
                
                // Получаем свежий blockhash
                const { blockhash } = await this.connection.getLatestBlockhash('finalized');
                closePositionTx.recentBlockhash = blockhash;
                
                console.log(`   ⚡ Отправка транзакции закрытия позиции...`);
                const signature = await sendAndConfirmTransaction(
                    this.connection,
                    closePositionTx,
                    [this.wallet],
                    { commitment: 'confirmed', maxRetries: 3 }
                );
                
                console.log(`   ✅ Позиция закрыта!`);
                console.log(`   📝 Signature: ${signature}`);
                
                return { success: true, signature, method: 'closePosition' };
            }
            
        } catch (error) {
            console.log(`   ❌ Ошибка: ${error.message}`);
            
            // Детальная диагностика
            if (error.message.includes('block height exceeded')) {
                console.log(`   🔍 Blockhash истек, попробуйте еще раз`);
            } else if (error.message.includes('position not found')) {
                console.log(`   🔍 Позиция не найдена или уже закрыта`);
            } else if (error.message.includes('insufficient funds')) {
                console.log(`   🔍 Недостаточно SOL для комиссии`);
            }
            
            return false;
        }
    }

    async run() {
        console.log('🔥 НАЧИНАЕМ ЗАКРЫТИЕ ПОЗИЦИЙ\n');
        
        // Проверяем баланс
        const balance = await this.connection.getBalance(this.wallet.publicKey);
        const solBalance = balance / 1e9;
        console.log(`💰 Баланс кошелька: ${solBalance.toFixed(6)} SOL`);
        
        if (solBalance < 0.001) {
            console.log('❌ Недостаточно SOL для комиссий!');
            return false;
        }
        
        let successCount = 0;
        const results = [];
        
        // Закрываем каждую позицию
        for (const position of this.positions) {
            const result = await this.closePosition(position);
            
            if (result && result.success) {
                successCount++;
                results.push(result);
            }
            
            // Пауза между операциями
            await new Promise(resolve => setTimeout(resolve, 3000));
        }
        
        // Итоги
        console.log(`\n🎉 РЕЗУЛЬТАТЫ:`);
        console.log(`   ✅ Закрыто позиций: ${successCount}/${this.positions.length}`);
        
        if (results.length > 0) {
            console.log(`   📝 Транзакции:`);
            results.forEach((result, index) => {
                console.log(`      ${index + 1}. ${result.signature} (${result.method})`);
            });
        }
        
        return successCount > 0;
    }
}

// 🚀 ЗАПУСК
async function main() {
    try {
        const closer = new WorkingPositionCloser();
        const success = await closer.run();
        
        console.log(success ? '\n✅ ГОТОВО!' : '\n❌ НИЧЕГО НЕ ЗАКРЫТО');
        process.exit(success ? 0 : 1);
        
    } catch (error) {
        console.error(`\n💥 ОШИБКА: ${error.message}`);
        process.exit(1);
    }
}

main();
