## EIP-1559 testing

This test contains testcases for EIP-1559, which were reported by <PERSON><PERSON> as misbehaving. 

```
[user@work evm]$ dir=./testdata/10 && ./evm t8n --state.fork=London --input.alloc=$dir/alloc.json --input.txs=$dir/txs.json --input.env=$dir/env.json --output.alloc=stdout --output.result=stdout 2>&1
INFO [05-09|22:11:59.436] rejected tx                              index=3 hash=db07bf..ede1e8 from=0xd02d72E067e77158444ef2020Ff2d325f929B363 error="gas limit reached"
```
Output:
```json
{
  "alloc": {
    "0x1111111111111111111111111111111111111111": {
      "code": "0xfe",
      "balance": "0x10000000000",
      "nonce": "0x1"
    },
    "0xa94f5374fce5edbc8e2a8697c15331677e6ebf0b": {
      "balance": "0x10000000000",
      "nonce": "0x1"
    },
    "0xd02d72e067e77158444ef2020ff2d325f929b363": {
      "balance": "0xff5beffffc95",
      "nonce": "0x4"
    }
  },
  "result": {
    "stateRoot": "0xf91a7ec08e4bfea88719aab34deabb000c86902360532b52afa9599d41f2bb8b",
    "txRoot": "0xda925f2306a52fa24c15d5cd212d736ee016415fd8dd0c45fd368de7917d64bb",
    "receiptsRoot": "0x439a25f7fc424c10fb1f89800e4aa1df74156b137239d9ac3eaa7c911c353cd5",
    "logsHash": "0x1dcc4de8dec75d7aab85b567b6ccd41ad312451b948a7413f0a142fd40d49347",
    "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000",
    "receipts": [
      {
        "type": "0x2",
        "root": "0x",
        "status": "0x0",
        "cumulativeGasUsed": "0x10000001",
        "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000",
        "logs": null,
        "transactionHash": "0x88980f6efcc5358d9c359663e7b9414722d430497637340ea056b076bc206701",
        "contractAddress": "0x0000000000000000000000000000000000000000",
        "gasUsed": "0x10000001",
        "blockHash": "0x0000000000000000000000000000000000000000000000000000000000000000",
        "transactionIndex": "0x0"
      },
      {
        "type": "0x2",
        "root": "0x",
        "status": "0x0",
        "cumulativeGasUsed": "0x20000001",
        "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000",
        "logs": null,
        "transactionHash": "0xd7bf3886f4e2aef74d525ae072c680f3846f550254401b67cbfda4a233757582",
        "contractAddress": "0x0000000000000000000000000000000000000000",
        "gasUsed": "0x10000000",
        "blockHash": "0x0000000000000000000000000000000000000000000000000000000000000000",
        "transactionIndex": "0x1"
      },
      {
        "type": "0x2",
        "root": "0x",
        "status": "0x0",
        "cumulativeGasUsed": "0x30000001",
        "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000",
        "logs": null,
        "transactionHash": "0x50308296760f01f1eeec7500e9e73cad67469249b1f59e9a9f55e6625a4923db",
        "contractAddress": "0x0000000000000000000000000000000000000000",
        "gasUsed": "0x10000000",
        "blockHash": "0x0000000000000000000000000000000000000000000000000000000000000000",
        "transactionIndex": "0x2"
      }
    ],
    "rejected": [
      {
        "index": 3,
        "error": "gas limit reached"
      }
    ],
    "currentDifficulty": "0x20000",
    "gasUsed": "0x30000001",
    "currentBaseFee": "0x36b"
  }
}
```
