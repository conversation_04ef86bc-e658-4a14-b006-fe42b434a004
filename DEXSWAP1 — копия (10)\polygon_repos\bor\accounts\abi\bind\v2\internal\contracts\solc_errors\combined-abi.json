{"contracts": {"contract.sol:C": {"abi": [{"inputs": [{"internalType": "uint256", "name": "arg1", "type": "uint256"}, {"internalType": "uint256", "name": "arg2", "type": "uint256"}, {"internalType": "uint256", "name": "arg3", "type": "uint256"}, {"internalType": "bool", "name": "arg4", "type": "bool"}], "name": "BadThing", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "arg1", "type": "uint256"}, {"internalType": "uint256", "name": "arg2", "type": "uint256"}, {"internalType": "uint256", "name": "arg3", "type": "uint256"}, {"internalType": "uint256", "name": "arg4", "type": "uint256"}], "name": "BadThing2", "type": "error"}, {"inputs": [], "name": "Bar", "outputs": [], "stateMutability": "pure", "type": "function"}, {"inputs": [], "name": "Foo", "outputs": [], "stateMutability": "pure", "type": "function"}], "bin": "6080604052348015600e575f5ffd5b506101c58061001c5f395ff3fe608060405234801561000f575f5ffd5b5060043610610034575f3560e01c8063b0a378b014610038578063bfb4ebcf14610042575b5f5ffd5b61004061004c565b005b61004a610092565b005b5f6001600260036040517fd233a24f00000000000000000000000000000000000000000000000000000000815260040161008994939291906100ef565b60405180910390fd5b5f600160025f6040517fbb6a82f10000000000000000000000000000000000000000000000000000000081526004016100ce949392919061014c565b60405180910390fd5b5f819050919050565b6100e9816100d7565b82525050565b5f6080820190506101025f8301876100e0565b61010f60208301866100e0565b61011c60408301856100e0565b61012960608301846100e0565b95945050505050565b5f8115159050919050565b61014681610132565b82525050565b5f60808201905061015f5f8301876100e0565b61016c60208301866100e0565b61017960408301856100e0565b610186606083018461013d565b9594505050505056fea26469706673582212206a82b4c28576e4483a81102558271cfefc891cd63b95440dea521185c1ff6a2a64736f6c634300081c0033"}, "contract.sol:C2": {"abi": [{"inputs": [{"internalType": "uint256", "name": "arg1", "type": "uint256"}, {"internalType": "uint256", "name": "arg2", "type": "uint256"}, {"internalType": "uint256", "name": "arg3", "type": "uint256"}, {"internalType": "bool", "name": "arg4", "type": "bool"}], "name": "BadThing", "type": "error"}, {"inputs": [], "name": "Foo", "outputs": [], "stateMutability": "pure", "type": "function"}], "bin": "6080604052348015600e575f5ffd5b506101148061001c5f395ff3fe6080604052348015600e575f5ffd5b50600436106026575f3560e01c8063bfb4ebcf14602a575b5f5ffd5b60306032565b005b5f600160025f6040517fbb6a82f1000000000000000000000000000000000000000000000000000000008152600401606c949392919060a3565b60405180910390fd5b5f819050919050565b6085816075565b82525050565b5f8115159050919050565b609d81608b565b82525050565b5f60808201905060b45f830187607e565b60bf6020830186607e565b60ca6040830185607e565b60d560608301846096565b9594505050505056fea2646970667358221220e90bf647ffc897060e44b88d54995ed0c03c988fbccaf034375c2ff4e594690764736f6c634300081c0033"}}, "version": "0.8.28+commit.7893614a.Darwin.appleclang"}